<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Resume Builder</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .dashboard-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 2rem 0;
        }
        .resume-card {
            border: 1px solid #dee2e6;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            background: white;
        }
        .resume-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        .create-resume-card {
            border: 2px dashed #dee2e6;
            border-radius: 12px;
            padding: 3rem 1.5rem;
            text-align: center;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .create-resume-card:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .btn-create {
            background-color: #28a745;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
        }
        .btn-edit {
            background-color: #007bff;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            margin-right: 8px;
        }
        .btn-delete {
            background-color: #dc3545;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
        }
        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 700;
        }
        .user-menu {
            background-color: #333;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white py-3 shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="/">Resume Builder</a>
            <div class="navbar-nav ms-auto">
                <a href="/login" class="user-menu">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Dashboard Header -->
    <section class="dashboard-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <h1 class="display-5 fw-bold mb-2">My Resumes</h1>
                    <p class="lead">Create and manage your professional resumes</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Dashboard Content -->
    <section class="py-5">
        <div class="container">
            <div class="row">
                <!-- Create New Resume Card -->
                <div class="col-lg-4 col-md-6">
                    <div class="create-resume-card">
                        <div class="mb-3">
                            <i class="fas fa-plus-circle" style="font-size: 3rem; color: #28a745;"></i>
                            <span style="font-size: 3rem; color: #28a745;">➕</span>
                        </div>
                        <h5 class="fw-bold mb-3">Create New Resume</h5>
                        <p class="text-muted mb-3">Start building your professional resume from scratch</p>
                        <button class="btn btn-create text-white" data-bs-toggle="modal" data-bs-target="#createResumeModal">
                            Create Resume
                        </button>
                    </div>
                </div>

                <!-- Sample Resume Cards (these would be dynamic from database) -->
                <div class="col-lg-4 col-md-6">
                    <div class="resume-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="fw-bold mb-0">Software Developer Resume</h5>
                            <span class="badge bg-success">Active</span>
                        </div>
                        <p class="text-muted mb-3">Last updated: 2 days ago</p>
                        <div class="d-flex">
                            <button class="btn btn-edit text-white btn-sm">Edit</button>
                            <button class="btn btn-delete text-white btn-sm">Delete</button>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6">
                    <div class="resume-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="fw-bold mb-0">Marketing Manager Resume</h5>
                            <span class="badge bg-secondary">Draft</span>
                        </div>
                        <p class="text-muted mb-3">Last updated: 1 week ago</p>
                        <div class="d-flex">
                            <button class="btn btn-edit text-white btn-sm">Edit</button>
                            <button class="btn btn-delete text-white btn-sm">Delete</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Create Resume Modal -->
    <div class="modal fade" id="createResumeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Resume</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form action="/create-resume" method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="resumeTitle" class="form-label">Resume Title</label>
                            <input type="text" class="form-control" id="resumeTitle" name="title" 
                                   placeholder="e.g., Software Developer Resume" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Resume</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
