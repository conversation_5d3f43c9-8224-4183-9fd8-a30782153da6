<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - SmartHire Resume Builder</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='bootstrap.min.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='style.css') }}"
    />
    <style>
      body {
        background: rgba(0, 0, 0, 0.5);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
      }

      .modal-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        width: 100%;
        max-width: 400px;
        position: relative;
        overflow: hidden;
      }

      .modal-header {
        padding: 2rem 2rem 1rem;
        text-align: center;
        position: relative;
      }

      .close-btn {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #999;
        cursor: pointer;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .close-btn:hover {
        color: #333;
      }

      .modal-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
      }

      .modal-subtitle {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 0;
      }

      .modal-body {
        padding: 1rem 2rem 2rem;
      }

      .form-group {
        margin-bottom: 1rem;
      }

      .form-label {
        display: block;
        font-size: 0.9rem;
        font-weight: 500;
        color: #333;
        margin-bottom: 0.5rem;
      }

      .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        font-size: 1rem;
        background: #f8f9fa;
        transition: all 0.3s ease;
        box-sizing: border-box;
      }

      .form-control:focus {
        outline: none;
        border-color: #007bff;
        background: white;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
      }

      .password-field {
        position: relative;
      }

      .password-toggle {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 1.1rem;
      }

      .btn-primary {
        width: 100%;
        background: #000;
        border: none;
        border-radius: 8px;
        padding: 12px;
        font-size: 1rem;
        font-weight: 600;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 1rem;
      }

      .btn-primary:hover {
        background: #333;
      }

      .modal-footer {
        text-align: center;
        padding: 0 2rem 2rem;
        font-size: 0.9rem;
        color: #666;
      }

      .modal-footer a {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
      }

      .modal-footer a:hover {
        text-decoration: underline;
      }

      .signup-icon {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        position: relative;
      }

      .signup-icon::before {
        content: "👤";
        font-size: 1.5rem;
      }

      .signup-icon::after {
        content: "+";
        position: absolute;
        bottom: -5px;
        right: -5px;
        background: #007bff;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: bold;
      }

      .demo-info {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 0.75rem;
        margin-top: 1rem;
        font-size: 0.8rem;
        color: #666;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <!-- Login Modal -->
    <div class="modal-container" id="loginModal">
      <div class="modal-header">
        <button class="close-btn" onclick="goHome()">&times;</button>
        <h2 class="modal-title">Welcome Back!</h2>
        <p class="modal-subtitle">Please enter your details to log in</p>
      </div>

      <form method="POST" action="/login">
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">Email Address</label>
            <input
              type="text"
              class="form-control"
              name="username"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label">Password</label>
            <div class="password-field">
              <input
                type="password"
                class="form-control"
                name="password"
                placeholder="Min 8 Characters"
                required
              />
              <button
                type="button"
                class="password-toggle"
                onclick="togglePassword(this)"
              >
                👁️
              </button>
            </div>
          </div>

          <button type="submit" class="btn-primary">LOGIN</button>

          <div class="demo-info">
            Demo: <strong>admin</strong> / <strong>admin123</strong>
          </div>
        </div>
      </form>

      <div class="modal-footer">
        Don't have an account? <a href="#" onclick="showSignup()">Sign Up</a>
      </div>
    </div>

    <!-- Signup Modal -->
    <div class="modal-container" id="signupModal" style="display: none">
      <div class="modal-header">
        <button class="close-btn" onclick="goHome()">&times;</button>
        <h2 class="modal-title">Create an Account</h2>
        <p class="modal-subtitle">
          Join us today by entering your details below
        </p>
        <div class="signup-icon"></div>
      </div>

      <form method="POST" action="/signup">
        <div class="modal-body">
          <div class="form-group">
            <label class="form-label">Full Name</label>
            <input
              type="text"
              class="form-control"
              name="fullname"
              placeholder="John"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label">Email Address</label>
            <input
              type="email"
              class="form-control"
              name="email"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div class="form-group">
            <label class="form-label">Password</label>
            <div class="password-field">
              <input
                type="password"
                class="form-control"
                name="password"
                placeholder="Min 8 Characters"
                required
              />
              <button
                type="button"
                class="password-toggle"
                onclick="togglePassword(this)"
              >
                👁️
              </button>
            </div>
          </div>

          <button type="submit" class="btn-primary">SIGN UP</button>
        </div>
      </form>

      <div class="modal-footer">
        Already an account? <a href="#" onclick="showLogin()">Login</a>
      </div>
    </div>

    <script>
      function togglePassword(btn) {
        const input = btn.previousElementSibling;
        if (input.type === "password") {
          input.type = "text";
          btn.textContent = "🙈";
        } else {
          input.type = "password";
          btn.textContent = "👁️";
        }
      }

      function showSignup() {
        document.getElementById("loginModal").style.display = "none";
        document.getElementById("signupModal").style.display = "block";
      }

      function showLogin() {
        document.getElementById("signupModal").style.display = "none";
        document.getElementById("loginModal").style.display = "block";
      }

      function goHome() {
        window.location.href = "/";
      }
    </script>
  </body>
</html>
