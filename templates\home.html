<!-- SmartHire - Home Page -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="keywords"
      content="resume, resume builder, resume builder online, resume builder website, resume builder software, resume builder app, resume builder free, resume builder online free, resume builder website free, resume builder software free, resume builder app free"
    />
    <meta
      name="description"
      content="Build your resume effortlessly with our smart and intuitive resume builder. Create a standout resume in minutes with our modern and professional templates."
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SmartHire - Home Page</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='bootstrap.min.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='style.css') }}"
    />
    <style>
      .hero-section {
        background: linear-gradient(135deg, #e8f2ff 0%, #d1e7ff 100%);
        min-height: 80vh;
        display: flex;
        align-items: center;
      }
      .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        color: #000;
        margin-bottom: 1rem;
      }
      .hero-highlight {
        color: #8b45ff;
      }
      .hero-blue {
        color: #8b45ff;
      }
      .hero-subtitle {
        font-size: 1.2rem;
        color: #000;
        margin-bottom: 2rem;
        line-height: 1.6;
      }
      .cta-button {
        background-color: #8b45ff;
        color: white;
        padding: 15px 30px;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(139, 69, 255, 0.15);
      }
      .cta-button:hover {
        background-color: #8b45ff;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(139, 69, 255, 0.25);
      }
      .resume-preview {
        position: relative;
      }
      .resume-template {
        width: 120px;
        height: 150px;
        background: linear-gradient(135deg, #e8f2ff 0%, #d1e7ff 100%);
        border: none;
        border-radius: 8px;
        margin: 10px;
        transition: transform 0.3s ease;
        box-shadow: 0 4px 12px rgba(139, 69, 255, 0.15);
      }
      .resume-template:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 16px rgba(139, 69, 255, 0.25);
      }
      .features-section {
        padding: 80px 0;
        background: linear-gradient(135deg, #e8f2ff 0%, #d1e7ff 100%);
      }
      .feature-card {
        text-align: center;
        padding: 2rem;
        border-radius: 12px;
        transition: transform 0.3s ease;
      }
      .feature-card:hover {
        transform: translateY(-5px);
      }
      .feature-icon {
        width: 60px;
        height: 60px;
        background: #8b45ff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: white;
        font-size: 1.5rem;
      }
      .navbar-brand {
        font-size: 1.5rem;
        font-weight: 700;
        color: #000 !important;
      }
      .login-btn {
        background-color: #8b45ff;
        color: white;
        padding: 8px 20px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
      }
      .login-btn:hover {
        background-color: #8b45ff;
        color: white;
        text-decoration: none;
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white py-3">
      <div class="container d-flex justify-content-between align-items-center">
        <a class="navbar-brand" href="/">SmartHire</a>
        <div class="navbar-nav">
          <a href="/login" class="login-btn">Login / Sign Up</a>
        </div>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6">
            <h1 class="hero-title">
              Build Your <span class="hero-highlight">Resume</span><br />
              <span class="hero-blue">Effortlessly</span>
            </h1>
            <p class="hero-subtitle">
              Craft a standout resume in minutes with our smart and intuitive
              resume builder.
            </p>
            <a href="/login" class="cta-button">Get Started</a>
          </div>
          <div class="col-lg-6">
            <div class="resume-preview d-flex flex-wrap justify-content-center">
              <!-- Resume Template Previews -->
              <div class="resume-template"></div>
              <div class="resume-template"></div>
              <div class="resume-template"></div>
              <div class="resume-template"></div>
              <div class="resume-template"></div>
              <div class="resume-template"></div>
              <div class="resume-template"></div>
              <div class="resume-template"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="container">
        <div class="row">
          <div class="col-12 text-center mb-5">
            <h2 class="display-5 fw-bold">Features That Make You Shine</h2>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-4 mb-4">
            <div class="feature-card">
              <div class="feature-icon">✏️</div>
              <h4 class="fw-bold mb-3">Easy Editing</h4>
              <p class="text-muted">
                Update your resume sections with live preview and instant
                formatting.
              </p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="feature-card">
              <div class="feature-icon">🎨</div>
              <h4 class="fw-bold mb-3">Beautiful Templates</h4>
              <p class="text-muted">
                Choose from modern, professional templates that are easy to
                customize.
              </p>
            </div>
          </div>
          <div class="col-lg-4 mb-4">
            <div class="feature-card">
              <div class="feature-icon">📄</div>
              <h4 class="fw-bold mb-3">One-Click Export</h4>
              <p class="text-muted">
                Download your resume instantly as a high-quality PDF with one
                click.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </body>
</html>
